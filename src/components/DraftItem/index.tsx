import React, { useEffect, useRef, useState } from "react";
import cn from "classnames";

import { AnswersService, NgpVanService, FileService, ClientService } from "services";
import * as interfaces from "interfaces";
import useClickOutside from "hooks/useClickOutside.hook";
import { useNotification } from "hooks";
import { useServiceContext } from "services/ServiceProvider";

import SavedList from "popups/SavedList";
import AnswerSavedListInfo from "components/AnswerSavedListInfo";
import EditTranscription from "popups/EditTranscription";

import IconAvatar from "shared/IconAvatar";
import Button from "shared/Button";

import classes from "./DratfItem.module.scss";
import VideoReplaceModal from "../VideoHelpers/VideoReplace/VideoReplaceModal";

export default function AnswerItem(props: {
  answer: interfaces.AnswerInterface;
  answersService: AnswersService;
  setAnswers: any;
  dropdownOpened: boolean;
  setOpenedDropdownId: any;
  ngpVanService: NgpVanService;
  fileService: FileService;
  clientService?: ClientService;
  user?: interfaces.UserInterface;
}) {
  const dropdownRef = useRef(null);
  const { isOutside: isOutsideDropdown, setIsOutside: setIsOutsideDropdown } =
    useClickOutside(dropdownRef);

  const [guideOpen, setGuideOpen] = useState(false);
  const [addTranscriptionOpen, setAddTranscriptionOpen] = useState(false);
  const [replaceVideoOpen, setReplaceVideoOpen] = useState(false);
  const [savedListInfo, setSavedListInfo] = useState<any>({});
  const [videoOpened, setVideoOpened] = useState(false);
  const { showAlert } = useNotification();
  const { adminStatsService } = useServiceContext();

  const {
    answer,
    setAnswers,
    dropdownOpened,
    setOpenedDropdownId,
    answersService,
    ngpVanService,
    fileService,
    clientService,
    user,
  } = props;

  let thumbnailStyle = {
    backgroundImage: videoOpened ? "" : "url(" + answer.imageUrl + ")",
  };

  const handlePostVideo = () => {
    const payload = {
      id: answer.id,
      clientId: answer.clientId,
      isDraft: false,
      isApproved: true,
      isDenied: false,
    };

    adminStatsService?.trackEvent('Drafts', 'post_video');

    answersService
      ?.updateAnswer(payload, () => answersService.getAnswers(setAnswers))
      .then(() => {
        showAlert("Video posted successfully");
        adminStatsService?.trackEvent('Drafts', 'video_posted_successfully');
      });
  };

  const handleDenyVideo = () => {
    const payload = {
      id: answer.id,
      clientId: answer.clientId,
      isApproved: false,
      isDenied: true,
    };

    adminStatsService?.trackEvent('Drafts', user?.client?.clientType === "Government" ? 'remove_video' : 'deny_video');

    answersService?.updateAnswer(payload).then(() => {
      answersService
        ?.removeAnswer(answer.id, () => answersService.getAnswers(setAnswers))
        .then(() => {
          showAlert("Answer denied");
          adminStatsService?.trackEvent('Drafts', 'video_removed_successfully');
        });
    });
  };

  const handleDropdownClick = () => {
    const newState = !dropdownOpened;
    setOpenedDropdownId(newState ? answer.id : null);
    adminStatsService?.trackEvent('Drafts', newState ? 'open_dropdown' : 'close_dropdown');
  };

  const handleDownloadAnswer = () => {
    adminStatsService?.trackEvent('Drafts', 'download_video');
    // Use video proxy to ensure proper content-type headers for QuickTime compatibility
    const proxyUrl = `${answersService?.base}/api/v1.0.0/video-proxy?url=${encodeURIComponent(answer.videoUrl)}`;
    window.location.href = proxyUrl;
  };

  useEffect(() => {
    if (isOutsideDropdown) {
      setOpenedDropdownId(null);
      setIsOutsideDropdown(false);
    }
  }, [isOutsideDropdown, setOpenedDropdownId, setIsOutsideDropdown]);

  return (
    <div
      className={cn(
        classes.DraftItem,
        answer.sentBy && classes.SentByHighlight
      )}
      data-answer-id={answer.id}
    >
      {answer.sentBy && (
        <div className={classes.SentBy}>Sent by: {answer.sentBy}</div>
      )}
      <div className={classes.DraftWrapper}>
        <div className={classes.thumbnail} style={thumbnailStyle}>
          {videoOpened ? (
            <video
              width="100%"
              height="auto"
              controls
              src={answer.videoUrl}
              autoPlay
              playsInline
              onEnded={() => setVideoOpened(false)}
            />
          ) : (
            <Button
              text=""
              iconText="play"
              callback={() => {
                setVideoOpened(true);
                adminStatsService?.trackEvent('Drafts', 'play_video');
              }}
            />
          )}
        </div>
        <div className={classes.meta}>
          <div className={classes.user}>
            <IconAvatar
              imageUrl={answer.question.user?.imageUrl}
              categoryIcon={answer.question.category}
            />
            <span className={classes.name}>
              {answer.question.overridingName || `${answer.question.user.firstName} ${answer.question.user.lastName}`}
            </span>
          </div>

          <div className={classes.buttonBar}>
            <Button text="Post" callback={handlePostVideo} />
            <Button
              text={
                user?.client?.clientType === "Government" ? "Remove" : "Deny"
              }
              callback={handleDenyVideo}
            />
            <Button
              text=""
              iconText={dropdownOpened ? "caret-up" : "caret-down"}
              callback={handleDropdownClick}
            />
            {dropdownOpened && (
              <div ref={dropdownRef} className={classes.dropdownBar}>
                <Button
                  text="Download Video"
                  iconText="download"
                  callback={handleDownloadAnswer}
                />
                <Button
                  text="Replace Video"
                  iconText="cloud-upload-alt"
                  callback={() => {
                    setReplaceVideoOpen(true);
                    adminStatsService?.trackEvent('Drafts', 'replace_video');
                  }}
                />
                <Button
                  text="Edit Transcription"
                  iconText="font"
                  callback={() => {
                    setAddTranscriptionOpen(true);
                    adminStatsService?.trackEvent('Drafts', 'edit_transcription');
                  }}
                />
              </div>
            )}
          </div>

          <div className={classes.question}>
            <div className={classes.questionText}>{answer.question.text}</div>
            <div className={classes.attributes}>
              <div className={classes.votes}>{answer.votes} votes </div>
              <div className={classes.time}>
                {" "}
                &nbsp; &bull; {answer.createdAtDateString}
              </div>
            </div>
            {savedListInfo.entryCount > 0 && (
              <AnswerSavedListInfo savedListInfo={savedListInfo} />
            )}
          </div>

          <SavedList
            isOpen={guideOpen}
            setSavedListInfo={setSavedListInfo}
            handleClose={() => setGuideOpen(false)}
            ngpVanService={ngpVanService}
            answerId={answer.id}
            user={user}
          />

          {addTranscriptionOpen && (
            <EditTranscription
              handleClose={() => setAddTranscriptionOpen(false)}
              transcription={answer.transcription || ""}
              answerId={answer.id}
              clientId={answer.clientId}
              answersService={answersService}
              setAnswers={setAnswers}
            />
          )}

          {replaceVideoOpen && (
            <VideoReplaceModal
              onClose={() => {
                answersService.getAnswers(setAnswers);
                setReplaceVideoOpen(false);
              }}
              answer={answer}
              question={answer.question}
              clientService={clientService}
            />
          )}

          {/* <NgpVideoGuide isOpen={guideOpen} handleClose={() => setGuideOpen(false)}/> */}
          {/* <div className="answer-saved-list-info${ wasSent ? ' active' : '' }">{ngpVanSentInformation}</div> */}
        </div>
      </div>
    </div>
  );
}
