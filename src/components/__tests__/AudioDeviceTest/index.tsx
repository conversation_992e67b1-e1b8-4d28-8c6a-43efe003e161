import React from 'react';
import { useAudioDevices } from '../../../hooks/useAudioDevices.hook';
import AudioDeviceSelector from '../../AudioDeviceSelector';

/**
 * Test component to verify audio device detection works correctly
 * This component can be temporarily added to any page to test the fix
 */
export const AudioDeviceTest: React.FC = () => {
  const { 
    audioDevices, 
    selectedDeviceId, 
    selectDevice, 
    isLoading, 
    error,
    audioConstraints 
  } = useAudioDevices();

  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

  return (
    <div className="p-4 border border-gray-300 rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Audio Device Test</h3>
      
      <div className="mb-4">
        <strong>Platform:</strong> {isIOS ? 'iOS' : 'Desktop/Android'}
      </div>
      
      <div className="mb-4">
        <AudioDeviceSelector
          audioDevices={audioDevices}
          selectedDeviceId={selectedDeviceId}
          selectDevice={selectDevice}
          isLoading={isLoading}
          error={error}
        />
      </div>
      
      <div className="mb-4">
        <strong>Status:</strong> {isLoading ? 'Loading...' : 'Ready'}
        {error && <div className="text-red-600 mt-1">{error}</div>}
      </div>
      
      <div className="mb-4">
        <strong>Devices Found:</strong> {audioDevices.length}
        <ul className="mt-2 ml-4">
          {audioDevices.map((device, index) => (
            <li key={device.deviceId} className="text-sm">
              {index + 1}. {device.label} (ID: {device.deviceId})
              {device.deviceId === selectedDeviceId && ' ← Selected'}
            </li>
          ))}
        </ul>
      </div>
      
      <div className="mb-4">
        <strong>Audio Constraints:</strong>
        <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
          {JSON.stringify(audioConstraints, null, 2)}
        </pre>
      </div>
      
      <div className="text-sm text-gray-600">
        <p><strong>Expected behavior:</strong></p>
        <ul className="ml-4 mt-1">
          <li>• <strong>iOS:</strong> Should show "System Microphone" with guidance text</li>
          <li>• <strong>Desktop:</strong> Should show actual device names and allow selection</li>
          <li>• <strong>RØDE on iOS:</strong> Connect device, select in Control Center, then refresh this page</li>
        </ul>
      </div>
    </div>
  );
};

export default AudioDeviceTest;
