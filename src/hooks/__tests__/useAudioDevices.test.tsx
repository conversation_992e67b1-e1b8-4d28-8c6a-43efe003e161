import { renderHook, waitFor } from '@testing-library/react';
import { useAudioDevices } from '../useAudioDevices.hook';

// Mock navigator.mediaDevices
const mockGetUserMedia = jest.fn();
const mockEnumerateDevices = jest.fn();
const mockAddEventListener = jest.fn();
const mockRemoveEventListener = jest.fn();

Object.defineProperty(global.navigator, 'mediaDevices', {
  value: {
    getUserMedia: mockGetUserMedia,
    enumerateDevices: mockEnumerateDevices,
    addEventListener: mockAddEventListener,
    removeEventListener: mockRemoveEventListener,
  },
  writable: true,
});

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
});

describe('useAudioDevices', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('should handle iOS devices correctly', async () => {
    // Mock iOS user agent
    Object.defineProperty(global.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      writable: true,
    });

    // Mock successful getUserMedia
    const mockStream = {
      getTracks: () => [{ stop: jest.fn() }],
    };
    mockGetUserMedia.mockResolvedValue(mockStream);

    // Mock iOS-style device enumeration (single generic device)
    mockEnumerateDevices.mockResolvedValue([
      {
        deviceId: 'default',
        kind: 'audioinput',
        label: '', // iOS often returns empty labels
        groupId: 'default',
      },
    ]);

    const { result } = renderHook(() => useAudioDevices());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.audioDevices).toHaveLength(1);
    expect(result.current.audioDevices[0].label).toBe('System Microphone');
    expect(result.current.audioDevices[0].deviceId).toBe('default');
    expect(result.current.audioConstraints).toBe(true); // iOS should use simple constraints
  });

  it('should handle desktop devices correctly', async () => {
    // Mock desktop user agent
    Object.defineProperty(global.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      writable: true,
    });

    // Mock successful getUserMedia
    const mockStream = {
      getTracks: () => [{ stop: jest.fn() }],
    };
    mockGetUserMedia.mockResolvedValue(mockStream);

    // Mock desktop-style device enumeration (multiple labeled devices)
    mockEnumerateDevices.mockResolvedValue([
      {
        deviceId: 'device1',
        kind: 'audioinput',
        label: 'Built-in Microphone',
        groupId: 'group1',
      },
      {
        deviceId: 'device2',
        kind: 'audioinput',
        label: 'RØDE PodMic',
        groupId: 'group2',
      },
    ]);

    const { result } = renderHook(() => useAudioDevices());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.audioDevices).toHaveLength(2);
    expect(result.current.audioDevices[0].label).toBe('Built-in Microphone');
    expect(result.current.audioDevices[1].label).toBe('RØDE PodMic');
    expect(result.current.selectedDeviceId).toBe('device1'); // Should select first device
  });

  it('should handle permission errors gracefully', async () => {
    mockGetUserMedia.mockRejectedValue(new Error('Permission denied'));

    const { result } = renderHook(() => useAudioDevices());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe('Could not access audio devices. Please check permissions.');
    expect(result.current.audioDevices).toHaveLength(0);
  });

  it('should generate correct audio constraints for selected device', async () => {
    // Mock desktop user agent
    Object.defineProperty(global.navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      writable: true,
    });

    const mockStream = {
      getTracks: () => [{ stop: jest.fn() }],
    };
    mockGetUserMedia.mockResolvedValue(mockStream);

    mockEnumerateDevices.mockResolvedValue([
      {
        deviceId: 'rode-mic',
        kind: 'audioinput',
        label: 'RØDE PodMic',
        groupId: 'group1',
      },
    ]);

    const { result } = renderHook(() => useAudioDevices());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should have device-specific constraints
    expect(result.current.audioConstraints).toEqual({
      deviceId: { exact: 'rode-mic' },
      echoCancellation: false,
      noiseSuppression: false,
      autoGainControl: false,
    });
  });
});
