import { useState, useEffect, useCallback, useMemo } from 'react';

interface AudioDevice {
  deviceId: string;
  label: string;
}

export const useAudioDevices = () => {
  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(
    () => localStorage.getItem('selectedAudioDevice')
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

  const getAudioDevices = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Step 1: Request permission with simple constraints (like online-voice-recorder.com)
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());

      // Step 2: Enumerate devices after permission is granted
      const devices = await navigator.mediaDevices.enumerateDevices();
      console.log('All devices after permission:', devices);

      // Step 3: Process audio input devices
      const audioInputs = devices.filter(d => d.kind === 'audioinput');
      console.log('Audio inputs found:', audioInputs);

      const processedDevices = audioInputs.map((device, index) => {
        console.log('Processing device:', {
          deviceId: device.deviceId,
          label: device.label,
          groupId: device.groupId
        });

        // iOS-friendly labeling
        let label = device.label;
        if (!label) {
          if (isIOS) {
            label = 'System Microphone';
          } else {
            label = `Microphone ${index + 1}`;
          }
        }

        return {
          deviceId: device.deviceId || 'default',
          label: label,
        };
      });

      console.log('Final processed devices:', processedDevices);
      setAudioDevices(processedDevices);

    } catch (err) {
      console.error('Audio device enumeration error:', err);
      setError('Could not access audio devices. Please check permissions.');
    } finally {
      setIsLoading(false);
    }
  }, [isIOS]);

  useEffect(() => {
    getAudioDevices();
    navigator.mediaDevices.addEventListener('devicechange', getAudioDevices);
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', getAudioDevices);
    };
  }, [getAudioDevices]);

  // useEffect(() => {
  //   if (audioDevices.length > 0) {
  //     const isValidSelection = audioDevices.some(d => d.deviceId === selectedDeviceId);
  //     if (!selectedDeviceId || !isValidSelection) {
  //       const newDeviceId = audioDevices[0].deviceId;
  //       setSelectedDeviceId(newDeviceId);
  //     }
  //   }
  // }, [audioDevices, selectedDeviceId]);

  useEffect(() => {
    if (selectedDeviceId) {
      localStorage.setItem('selectedAudioDevice', selectedDeviceId);
    }
  }, [selectedDeviceId]);

  const selectDevice = (deviceId: string) => {
    setSelectedDeviceId(deviceId);
  };

  const audioConstraints = useMemo(() => {
    if (isIOS) {
      return true; // Let iOS pick the mic
    }
    if (!selectedDeviceId) return true;
    return {
      deviceId: { exact: selectedDeviceId },
      echoCancellation: false,
      noiseSuppression: false,
      autoGainControl: false
    };
  }, [selectedDeviceId, isIOS]);

  return {
    audioDevices,
    selectedDeviceId,
    selectDevice,
    isLoading,
    error,
    audioConstraints,
  };
};
